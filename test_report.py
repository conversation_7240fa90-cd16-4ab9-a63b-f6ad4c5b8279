#!/usr/bin/env python3
"""
Comprehensive test report for the SSH process monitoring script.
This runs all tests and generates a detailed report.
"""

import subprocess
import sys
import json
from datetime import datetime


def run_test_suite():
    """Run all test suites and generate a comprehensive report."""
    
    print("="*70)
    print("SSH PROCESS MONITORING SCRIPT - COMPREHENSIVE TEST REPORT")
    print("="*70)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = {}
    
    # Test 1: Unit Tests
    print("1. UNIT TESTS")
    print("-" * 40)
    try:
        result = subprocess.run([sys.executable, 'test_script.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ PASSED - All unit tests successful")
            test_results['unit_tests'] = 'PASSED'
            
            # Extract test count
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Ran' in line and 'tests' in line:
                    print(f"   {line.strip()}")
                    break
        else:
            print("❌ FAILED - Unit tests failed")
            test_results['unit_tests'] = 'FAILED'
            print(f"   Error: {result.stderr}")
            
    except Exception as e:
        print(f"❌ ERROR - Could not run unit tests: {e}")
        test_results['unit_tests'] = 'ERROR'
    
    print()
    
    # Test 2: SSH Mock Tests
    print("2. SSH MOCK TESTS")
    print("-" * 40)
    try:
        result = subprocess.run([sys.executable, 'test_ssh_mock.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ PASSED - All SSH mock tests successful")
            test_results['ssh_mock_tests'] = 'PASSED'
            
            # Extract test count
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Ran' in line and 'tests' in line:
                    print(f"   {line.strip()}")
                    break
        else:
            print("❌ FAILED - SSH mock tests failed")
            test_results['ssh_mock_tests'] = 'FAILED'
            print(f"   Error: {result.stderr}")
            
    except Exception as e:
        print(f"❌ ERROR - Could not run SSH mock tests: {e}")
        test_results['ssh_mock_tests'] = 'ERROR'
    
    print()
    
    # Test 3: Integration Test
    print("3. INTEGRATION TEST")
    print("-" * 40)
    try:
        result = subprocess.run([sys.executable, 'test.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ PASSED - Integration test successful")
            test_results['integration_test'] = 'PASSED'
            
            # Check JSON output
            if '"records":' in result.stdout and '"timestamp":' in result.stdout:
                print("   ✅ JSON structure validated")
                
                # Try to count records
                try:
                    lines = result.stdout.split('\n')
                    json_start = -1
                    for i, line in enumerate(lines):
                        if line.strip() == '{':
                            json_start = i
                            break
                    
                    if json_start >= 0:
                        json_lines = lines[json_start:]
                        json_str = '\n'.join(json_lines).strip()
                        parsed_json = json.loads(json_str)
                        record_count = len(parsed_json.get('records', []))
                        print(f"   ✅ Processed {record_count} process records")
                        
                except Exception:
                    print("   ⚠️  Could not parse JSON details")
            else:
                print("   ⚠️  JSON structure not found")
                
        else:
            print("❌ FAILED - Integration test failed")
            test_results['integration_test'] = 'FAILED'
            print(f"   Error: {result.stderr}")
            
    except Exception as e:
        print(f"❌ ERROR - Could not run integration test: {e}")
        test_results['integration_test'] = 'ERROR'
    
    print()
    
    # Test 4: Dependency Check
    print("4. DEPENDENCY CHECK")
    print("-" * 40)
    dependencies = ['paramiko', 'json', 'pandas', 'datetime']
    all_deps_ok = True
    
    for dep in dependencies:
        try:
            if dep == 'datetime':
                # datetime is built-in
                result = subprocess.run([sys.executable, '-c', f'from {dep} import datetime'], 
                                      capture_output=True, text=True, timeout=10)
            else:
                result = subprocess.run([sys.executable, '-c', f'import {dep}'], 
                                      capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"   ✅ {dep} - Available")
            else:
                print(f"   ❌ {dep} - Missing")
                all_deps_ok = False
                
        except Exception as e:
            print(f"   ❌ {dep} - Error checking: {e}")
            all_deps_ok = False
    
    test_results['dependencies'] = 'PASSED' if all_deps_ok else 'FAILED'
    print()
    
    # Test Summary
    print("5. TEST SUMMARY")
    print("-" * 40)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result == 'PASSED')
    failed_tests = sum(1 for result in test_results.values() if result == 'FAILED')
    error_tests = sum(1 for result in test_results.values() if result == 'ERROR')
    
    print(f"Total Test Categories: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Errors: {error_tests}")
    print()
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! The script is ready for use.")
        overall_status = "PASSED"
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        overall_status = "FAILED"
    
    print()
    
    # Detailed Results
    print("6. DETAILED RESULTS")
    print("-" * 40)
    for test_name, result in test_results.items():
        status_icon = "✅" if result == "PASSED" else "❌" if result == "FAILED" else "⚠️"
        print(f"{status_icon} {test_name.replace('_', ' ').title()}: {result}")
    
    print()
    print("="*70)
    print(f"OVERALL STATUS: {overall_status}")
    print("="*70)
    
    return overall_status == "PASSED"


if __name__ == '__main__':
    success = run_test_suite()
    sys.exit(0 if success else 1)
