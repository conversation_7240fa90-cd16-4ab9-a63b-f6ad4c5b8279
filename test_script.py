#!/usr/bin/env python3
"""
Test suite for the SSH process monitoring script.
This tests various scenarios including data parsing, error handling, and JSON output.
"""

import unittest
import json
import sys
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import io

# Import the functions we want to test
# Since the original script is not modularized, we'll test the core logic


class TestProcessMonitoring(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_ps_output = """USER       PID COMMAND         %CPU %MEM
root         1 systemd          0.1  0.5
mysql      1234 mysqld           5.2  12.3
apache     5678 httpd            3.1  8.7
root       9012 python3          2.8  4.2
user1      3456 firefox          1.9  15.6"""
        
        self.expected_processes = [
            {'user': 'root', 'pid': 1, 'command': 'systemd', 'cpu_percent': 0.1, 'memory_percent': 0.5},
            {'user': 'mysql', 'pid': 1234, 'command': 'mysqld', 'cpu_percent': 5.2, 'memory_percent': 12.3},
            {'user': 'apache', 'pid': 5678, 'command': 'httpd', 'cpu_percent': 3.1, 'memory_percent': 8.7},
            {'user': 'root', 'pid': 9012, 'command': 'python3', 'cpu_percent': 2.8, 'memory_percent': 4.2},
            {'user': 'user1', 'pid': 3456, 'command': 'firefox', 'cpu_percent': 1.9, 'memory_percent': 15.6}
        ]

    def test_parse_ps_output(self):
        """Test parsing of ps command output."""
        lines = self.sample_ps_output.strip().split('\n')
        processes = []
        
        for i, line in enumerate(lines):
            if i == 0:  # Skip header
                continue
                
            parts = line.split(None, 4)
            if len(parts) >= 5:
                process = {
                    'user': parts[0],
                    'pid': int(parts[1]),
                    'command': parts[2],
                    'cpu_percent': float(parts[3]),
                    'memory_percent': float(parts[4])
                }
                processes.append(process)
        
        self.assertEqual(len(processes), 5)
        self.assertEqual(processes, self.expected_processes)

    def test_servicenow_payload_structure(self):
        """Test ServiceNow payload structure."""
        test_ip = "*************"
        test_timestamp = "2025-07-29T12:00:00.000000"
        
        servicenow_payload = {
            'records': self.expected_processes,
            'timestamp': test_timestamp,
            'source_host': test_ip
        }
        
        # Verify payload structure
        self.assertIn('records', servicenow_payload)
        self.assertIn('timestamp', servicenow_payload)
        self.assertIn('source_host', servicenow_payload)
        
        # Verify records content
        self.assertEqual(len(servicenow_payload['records']), 5)
        self.assertEqual(servicenow_payload['source_host'], test_ip)
        
        # Verify JSON serialization works
        json_str = json.dumps(servicenow_payload, indent=2)
        self.assertIsInstance(json_str, str)
        
        # Verify JSON can be parsed back
        parsed_payload = json.loads(json_str)
        self.assertEqual(parsed_payload, servicenow_payload)

    def test_malformed_ps_output(self):
        """Test handling of malformed ps output."""
        malformed_output = """USER       PID COMMAND         %CPU %MEM
root         1 systemd          0.1  0.5
incomplete line
mysql      1234 mysqld           5.2  12.3
another incomplete
apache     5678 httpd            3.1  8.7"""
        
        lines = malformed_output.strip().split('\n')
        processes = []
        
        for i, line in enumerate(lines):
            if i == 0:  # Skip header
                continue
                
            parts = line.split(None, 4)
            if len(parts) >= 5:  # Only process complete lines
                try:
                    process = {
                        'user': parts[0],
                        'pid': int(parts[1]),
                        'command': parts[2],
                        'cpu_percent': float(parts[3]),
                        'memory_percent': float(parts[4])
                    }
                    processes.append(process)
                except (ValueError, IndexError):
                    # Skip malformed lines
                    continue
        
        # Should only have 3 valid processes (skipping malformed lines)
        self.assertEqual(len(processes), 3)
        self.assertEqual(processes[0]['user'], 'root')
        self.assertEqual(processes[1]['user'], 'mysql')
        self.assertEqual(processes[2]['user'], 'apache')

    def test_empty_ps_output(self):
        """Test handling of empty ps output."""
        empty_output = "USER       PID COMMAND         %CPU %MEM"
        
        lines = empty_output.strip().split('\n')
        processes = []
        
        for i, line in enumerate(lines):
            if i == 0:  # Skip header
                continue
                
            parts = line.split(None, 4)
            if len(parts) >= 5:
                process = {
                    'user': parts[0],
                    'pid': int(parts[1]),
                    'command': parts[2],
                    'cpu_percent': float(parts[3]),
                    'memory_percent': float(parts[4])
                }
                processes.append(process)
        
        self.assertEqual(len(processes), 0)

    def test_data_types(self):
        """Test that data types are correctly converted."""
        test_line = "root      1234 systemd          5.2  12.3"
        parts = test_line.split(None, 4)
        
        process = {
            'user': parts[0],
            'pid': int(parts[1]),
            'command': parts[2],
            'cpu_percent': float(parts[3]),
            'memory_percent': float(parts[4])
        }
        
        self.assertIsInstance(process['user'], str)
        self.assertIsInstance(process['pid'], int)
        self.assertIsInstance(process['command'], str)
        self.assertIsInstance(process['cpu_percent'], float)
        self.assertIsInstance(process['memory_percent'], float)
        
        self.assertEqual(process['pid'], 1234)
        self.assertEqual(process['cpu_percent'], 5.2)
        self.assertEqual(process['memory_percent'], 12.3)

    def test_json_serialization_with_datetime(self):
        """Test JSON serialization with datetime objects."""
        test_payload = {
            'records': self.expected_processes,
            'timestamp': datetime.now().isoformat(),
            'source_host': "*************"
        }
        
        # Should not raise an exception
        json_str = json.dumps(test_payload, indent=2)
        self.assertIsInstance(json_str, str)
        
        # Verify timestamp format
        parsed = json.loads(json_str)
        timestamp = parsed['timestamp']
        # Should be in ISO format
        self.assertRegex(timestamp, r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+')


def run_integration_test():
    """Run an integration test of the actual script."""
    print("\n" + "="*50)
    print("INTEGRATION TEST")
    print("="*50)
    
    try:
        # Import and run the actual script logic
        import subprocess
        result = subprocess.run([sys.executable, 'test.py'], 
                              capture_output=True, text=True, timeout=30)
        
        print(f"Return code: {result.returncode}")
        print(f"Output length: {len(result.stdout)} characters")
        
        if result.returncode == 0:
            print("[PASS] Script executed successfully")

            # Check if output contains expected JSON structure
            if '"records":' in result.stdout and '"timestamp":' in result.stdout:
                print("[PASS] JSON output structure looks correct")

                # Try to extract and parse the JSON
                lines = result.stdout.split('\n')
                json_start = -1
                for i, line in enumerate(lines):
                    if line.strip() == '{':
                        json_start = i
                        break

                if json_start >= 0:
                    json_lines = lines[json_start:]
                    json_str = '\n'.join(json_lines).strip()
                    try:
                        parsed_json = json.loads(json_str)
                        print(f"[PASS] JSON parsing successful - found {len(parsed_json.get('records', []))} process records")
                    except json.JSONDecodeError as e:
                        print(f"[FAIL] JSON parsing failed: {e}")
            else:
                print("[FAIL] Expected JSON structure not found in output")
        else:
            print(f"[FAIL] Script failed with return code {result.returncode}")
            print(f"Error output: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("[FAIL] Script timed out")
    except Exception as e:
        print(f"[FAIL] Integration test failed: {e}")


if __name__ == '__main__':
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    run_integration_test()
