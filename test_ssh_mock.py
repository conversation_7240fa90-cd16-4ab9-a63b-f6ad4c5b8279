#!/usr/bin/env python3
"""
Test the SSH functionality with mocked connections.
This simulates a successful SSH connection and command execution.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
from datetime import datetime


class TestSSHFunctionality(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_ps_output = """USER       PID COMMAND         %CPU %MEM
root         1 systemd          0.1  0.5
mysql      1234 mysqld           5.2  12.3
apache     5678 httpd            3.1  8.7
root       9012 python3          2.8  4.2
user1      3456 firefox          1.9  15.6
root       7890 sshd             0.8  1.2
postgres   2345 postgres         1.5  6.8
nginx      6789 nginx            0.7  2.1
root       4567 cron             0.2  0.3
user2      8901 chrome           4.5  18.9"""

    @patch('paramiko.SSHClient')
    def test_successful_ssh_connection(self, mock_ssh_client):
        """Test successful SSH connection and command execution."""
        # Set up mocks
        mock_client_instance = Mock()
        mock_ssh_client.return_value = mock_client_instance
        
        # Mock the command execution
        mock_stdout = Mock()
        mock_stdout.read.return_value = self.mock_ps_output.encode('utf-8')
        mock_stderr = Mock()
        mock_stderr.read.return_value = b''
        
        mock_client_instance.exec_command.return_value = (None, mock_stdout, mock_stderr)
        
        # Simulate the script logic
        IP = "*************"
        username = "testuser"
        password = "testpass"
        
        # Create client and connect
        client = mock_ssh_client()
        client.set_missing_host_key_policy(mock_ssh_client.AutoAddPolicy())
        client.connect(hostname=IP, username=username, password=password)
        
        # Execute command
        cmd = "ps ax eo user,pid,comm,pcpu,%mem --sort=-pcpu | head -n 11"
        stdin, stdout, stderr = client.exec_command(cmd)
        
        # Process output
        output = stdout.read().decode('utf-8')
        lines = output.strip().split('\n')
        processes = []
        
        for i, line in enumerate(lines):
            if i == 0:  # Skip header
                continue
                
            parts = line.split(None, 4)
            if len(parts) >= 5:
                process = {
                    'user': parts[0],
                    'pid': int(parts[1]),
                    'command': parts[2],
                    'cpu_percent': float(parts[3]),
                    'memory_percent': float(parts[4])
                }
                processes.append(process)
        
        # Create ServiceNow payload
        servicenow_payload = {
            'records': processes,
            'timestamp': datetime.now().isoformat(),
            'source_host': IP
        }
        
        # Verify results
        self.assertEqual(len(processes), 10)  # Should have 10 processes (excluding header)
        self.assertIn('records', servicenow_payload)
        self.assertIn('timestamp', servicenow_payload)
        self.assertIn('source_host', servicenow_payload)
        self.assertEqual(servicenow_payload['source_host'], IP)
        
        # Verify SSH client was called correctly
        mock_ssh_client.assert_called_once()
        mock_client_instance.connect.assert_called_once_with(
            hostname=IP, username=username, password=password
        )
        mock_client_instance.exec_command.assert_called_once_with(cmd)
        
        # Verify JSON serialization works
        json_str = json.dumps(servicenow_payload, indent=2)
        self.assertIsInstance(json_str, str)
        
        print(f"[PASS] Successfully processed {len(processes)} processes")
        print(f"[PASS] JSON payload size: {len(json_str)} characters")

    @patch('paramiko.SSHClient')
    def test_ssh_connection_failure(self, mock_ssh_client):
        """Test SSH connection failure handling."""
        # Set up mock to raise connection error
        mock_client_instance = Mock()
        mock_ssh_client.return_value = mock_client_instance
        mock_client_instance.connect.side_effect = Exception("Connection failed")
        
        # Simulate the script logic
        IP = "*************"
        username = "testuser"
        password = "testpass"
        
        client = mock_ssh_client()
        
        # This should raise an exception
        with self.assertRaises(Exception) as context:
            client.connect(hostname=IP, username=username, password=password)
        
        self.assertIn("Connection failed", str(context.exception))
        print("[PASS] Connection failure handled correctly")

    @patch('paramiko.SSHClient')
    def test_command_execution_error(self, mock_ssh_client):
        """Test command execution error handling."""
        # Set up mocks
        mock_client_instance = Mock()
        mock_ssh_client.return_value = mock_client_instance
        
        # Mock successful connection but failed command
        mock_stdout = Mock()
        mock_stdout.read.return_value = b''  # Empty output
        mock_stderr = Mock()
        mock_stderr.read.return_value = b'Command not found'
        
        mock_client_instance.exec_command.return_value = (None, mock_stdout, mock_stderr)
        
        # Simulate the script logic
        IP = "*************"
        username = "testuser"
        password = "testpass"
        
        client = mock_ssh_client()
        client.connect(hostname=IP, username=username, password=password)
        
        # Execute command
        cmd = "ps ax eo user,pid,comm,pcpu,%mem --sort=-pcpu | head -n 11"
        stdin, stdout, stderr = client.exec_command(cmd)
        
        # Process output
        output = stdout.read().decode('utf-8')
        error_output = stderr.read().decode('utf-8')
        
        # Verify error handling
        self.assertEqual(output, '')
        self.assertEqual(error_output, 'Command not found')

        print("[PASS] Command execution error handled correctly")


def run_mock_tests():
    """Run the mock SSH tests."""
    print("Running SSH Mock Tests...")
    print("="*50)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSSHFunctionality)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    if result.wasSuccessful():
        print("\n[PASS] All SSH mock tests passed!")
    else:
        print(f"\n[FAIL] {len(result.failures)} test(s) failed")
        print(f"[FAIL] {len(result.errors)} test(s) had errors")


if __name__ == '__main__':
    run_mock_tests()
