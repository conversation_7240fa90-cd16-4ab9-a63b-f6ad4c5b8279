# Establish an SSH connection
client = paramiko.SSHClient()
client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

client.connect(hostname = IP, username = username, password = password)

# Execute command on host
cmd = "ps ax eo user,pid,comm,pcpu,%mem --sort=-pcpu | head -n 11"
stdin, stdout, stderr = client.exec_command(cmd)
    
import json
import pandas as pd
from datetime import datetime

try:
    output = stdout.read().decode('utf-8')
    print("Raw output:")
    print(output)
    print("-" * 50)
    
    # Parse the data
    lines = output.strip().split('\n')
    processes = []
    
    for i, line in enumerate(lines):
        print(f"Line {i}: {line}")
        if i == 0:  # Skip header
            continue
            
        parts = line.split(None, 4)
        if len(parts) >= 5:
            process = {
                'user': parts[0],
                'pid': int(parts[1]),
                'command': parts[2],
                'cpu_percent': float(parts[3]),
                'memory_percent': float(parts[4])
            }
            processes.append(process)
    
    # Create ServiceNow payload
    servicenow_payload = {
        'records': processes,
        'timestamp': datetime.now().isoformat(),
        'source_host': IP
    }
    
    print("\nServiceNow JSON:")
    print(json.dumps(servicenow_payload, indent=2))
    
except Exception as e:
    print(f"Error: {e}")
    print(f"Error output: {stderr.read().decode('utf-8')}")

finally:
    client.close()
    
