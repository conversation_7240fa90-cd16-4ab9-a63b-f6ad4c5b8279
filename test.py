import paramiko
import json
import pandas as pd
from datetime import datetime

# Configuration - replace with actual values for real usage
IP = "*************"  # Replace with actual IP
username = "testuser"  # Replace with actual username
password = "testpass"  # Replace with actual password

# Establish an SSH connection
client = paramiko.SSHClient()
client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

try:
    client.connect(hostname=IP, username=username, password=password)
    print(f"Successfully connected to {IP}")
except Exception as e:
    print(f"Failed to connect to {IP}: {e}")
    # For testing purposes, we'll simulate the command output
    print("Using simulated data for testing...")

    # Simulated ps command output
    simulated_output = """USER       PID COMMAND         %CPU %MEM
root         1 systemd          0.1  0.5
mysql      1234 mysqld           5.2  12.3
apache     5678 httpd            3.1  8.7
root       9012 python3          2.8  4.2
user1      3456 firefox          1.9  15.6
root       7890 sshd             0.8  1.2
postgres   2345 postgres         1.5  6.8
nginx      6789 nginx            0.7  2.1
root       4567 cron             0.2  0.3
user2      8901 chrome           4.5  18.9"""

    # Process the simulated data
    lines = simulated_output.strip().split('\n')
    processes = []

    for i, line in enumerate(lines):
        print(f"Line {i}: {line}")
        if i == 0:  # Skip header
            continue

        parts = line.split(None, 4)
        if len(parts) >= 5:
            process = {
                'user': parts[0],
                'pid': int(parts[1]),
                'command': parts[2],
                'cpu_percent': float(parts[3]),
                'memory_percent': float(parts[4])
            }
            processes.append(process)

    # Create ServiceNow payload
    servicenow_payload = {
        'records': processes,
        'timestamp': datetime.now().isoformat(),
        'source_host': IP
    }

    print("\nServiceNow JSON:")
    print(json.dumps(servicenow_payload, indent=2))

    exit()  # Exit here since we're using simulated data

    # Execute command on host
    cmd = "ps ax eo user,pid,comm,pcpu,%mem --sort=-pcpu | head -n 11"
    stdin, stdout, stderr = client.exec_command(cmd)

    try:
        output = stdout.read().decode('utf-8')
        print("Raw output:")
        print(output)
        print("-" * 50)

        # Parse the data
        lines = output.strip().split('\n')
        processes = []

        for i, line in enumerate(lines):
            print(f"Line {i}: {line}")
            if i == 0:  # Skip header
                continue

            parts = line.split(None, 4)
            if len(parts) >= 5:
                process = {
                    'user': parts[0],
                    'pid': int(parts[1]),
                    'command': parts[2],
                    'cpu_percent': float(parts[3]),
                    'memory_percent': float(parts[4])
                }
                processes.append(process)

        # Create ServiceNow payload
        servicenow_payload = {
            'records': processes,
            'timestamp': datetime.now().isoformat(),
            'source_host': IP
        }

        print("\nServiceNow JSON:")
        print(json.dumps(servicenow_payload, indent=2))

    except Exception as e:
        print(f"Error: {e}")
        print(f"Error output: {stderr.read().decode('utf-8')}")

    finally:
        client.close()
    
